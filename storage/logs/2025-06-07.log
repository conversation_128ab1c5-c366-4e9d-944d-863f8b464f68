{"level":"error","timestamp":"2025-06-07T04:20:54.062Z","context":[],"errors":[],"metadata":{},"message":"JSON-RPC request #1 failed with error: [-32603] AggregateJsonRpcError: All endpoints failed"}
{"level":"error","timestamp":"2025-06-07T04:21:23.760Z","context":[],"errors":[],"metadata":{},"message":"JSON-RPC request #2 failed with error: [-32603] AggregateJsonRpcError: All endpoints failed"}
{"level":"fatal","timestamp":"2025-06-07T08:44:10.894Z","context":[],"errors":[{"name":"Error","message":"Unexpected server response: 403","stack":"Error: \n    at ClientRequest.<anonymous> (/Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/ws@8.18.2/node_modules/ws/lib/websocket.js:913:7)"}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-07T08:48:42.002Z","context":[],"errors":[{"name":"Error","message":"Unexpected server response: 403","stack":"Error: \n    at ClientRequest.<anonymous> (/Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/ws@8.18.2/node_modules/ws/lib/websocket.js:913:7)"}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-07T08:48:47.021Z","context":[],"errors":[{"errno":-3008,"code":"ENOTFOUND","syscall":"getaddrinfo","hostname":"rpc1.solanatracker.io","name":"Error","message":"getaddrinfo ENOTFOUND rpc1.solanatracker.io","stack":"Error: getaddrinfo ENOTFOUND rpc1.solanatracker.io\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)"}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-07T08:49:40.689Z","context":[],"errors":[{"errno":-3008,"code":"ENOTFOUND","syscall":"getaddrinfo","hostname":"rpc1.solanatracker.io","name":"Error","message":"getaddrinfo ENOTFOUND rpc1.solanatracker.io","stack":"Error: getaddrinfo ENOTFOUND rpc1.solanatracker.io\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:122:26)"}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-07T08:50:38.714Z","context":[],"errors":[{"name":"TimeoutError","message":"WebSocket request timed out","stack":"TimeoutError: \n    at new TimeoutError (/Users/<USER>/Projects/new-rpc-proxy/app/errors/timeout-error.ts:3:9)\n    at <anonymous> (/Users/<USER>/Projects/new-rpc-proxy/app/endpoints/websocket.ts:40:118)\n    at createTimeoutError (/Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/@kdt310722+utils@0.0.19/node_modules/@kdt310722/src/promise/deferred-with-timeout.ts:10:77)\n    at Timeout.<anonymous> (/Users/<USER>/Projects/new-rpc-proxy/node_modules/.pnpm/@kdt310722+utils@0.0.19/node_modules/@kdt310722/src/promise/deferred-with-timeout.ts:33:76)","code":23}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-07T08:52:06.010Z","context":[],"errors":[{"timestamp":"2025-06-07T08:52:06.010Z","code":-32601,"statusCode":404,"name":"JsonRpcError","message":"Method not found","stack":"JsonRpcError: \n    at Function.fromErrorObject (/Users/<USER>/Projects/new-rpc-proxy/app/errors/json-rpc-error.ts:43:16)\n    at EndpointWebSocketClient.send (/Users/<USER>/Projects/new-rpc-proxy/app/endpoints/websocket.ts:49:36)\n    at async <anonymous> (/Users/<USER>/Projects/new-rpc-proxy/test.ts:8:1)"}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-07T08:52:34.714Z","context":[],"errors":[{"timestamp":"2025-06-07T08:52:34.713Z","code":-32601,"statusCode":404,"name":"JsonRpcError","message":"Method not found","stack":"JsonRpcError: \n    at Function.fromErrorObject (/Users/<USER>/Projects/new-rpc-proxy/app/errors/json-rpc-error.ts:43:16)\n    at EndpointWebSocketClient.send (/Users/<USER>/Projects/new-rpc-proxy/app/endpoints/websocket.ts:49:36)\n    at async <anonymous> (/Users/<USER>/Projects/new-rpc-proxy/test.ts:8:1)"}],"metadata":{}}
{"level":"fatal","timestamp":"2025-06-07T08:53:03.547Z","context":[],"errors":[{"timestamp":"2025-06-07T08:53:03.546Z","code":-32601,"statusCode":404,"name":"JsonRpcError","message":"Method not found","stack":"JsonRpcError: \n    at Function.fromErrorObject (/Users/<USER>/Projects/new-rpc-proxy/app/errors/json-rpc-error.ts:43:16)\n    at EndpointWebSocketClient.send (/Users/<USER>/Projects/new-rpc-proxy/app/endpoints/websocket.ts:49:36)\n    at async <anonymous> (/Users/<USER>/Projects/new-rpc-proxy/test.ts:8:1)"}],"metadata":{}}
