import { websocket } from './app/config/endpoints'
import { logger } from './app/core/logger'
import { EndpointWebSocketClient } from './app/endpoints/websocket'

const config = websocket.parse('wss://solana-rpc.publicnode.com')!
const client = new EndpointWebSocketClient('Test', config, logger)

await Promise.all([
    client.connect(),
    client.connect(),
    client.connect(),
    client.connect(),
])

await Promise.all([
    client.subscribeSlot((slot) => null),
    client.subscribeSlot((slot) => null),
    client.subscribeSlot((slot) => null),
])
