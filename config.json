{"endpoints": [{"name": "NuFi", "enabled": true, "http": {"url": "https://solana-mainnet.nu.fi", "maxRequestsPerSecond": 10}}, {"name": "SolFlare", "enabled": true, "http": {"url": "https://wallet-api.solflare.com/v2/tx/rpc-extended/mainnet", "maxRequestsPerSecond": 10}}, {"name": "PublicNode", "enabled": true, "http": {"url": "https://solana-rpc.publicnode.com", "maxRequestsPerSecond": 10}, "websocket": "wss://solana-rpc.publicnode.com"}]}