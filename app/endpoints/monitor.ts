import { Emitter } from '@kdt310722/utils/event'
import { BigIntMath } from '@kdt310722/utils/number'

export interface EndpointStats {
    totalRequests: number
    successCount: number
    failedCount: number
    rateLimitReachedCount: number
    minResponseTime: bigint
    maxResponseTime: bigint
    avgResponseTime: bigint
    updatedAt: number
}

export type EndpointMonitorEvents = {
    stats: (stats: EndpointStats) => void
}

export class EndpointMonitor extends Emitter<EndpointMonitorEvents> {
    public readonly stats = { totalRequests: 0, successCount: 0, failedCount: 0, rateLimitReachedCount: 0, updatedAt: Date.now() } as EndpointStats

    public increaseSuccessRequest(took: bigint) {
        this.stats.successCount++
        this.stats.totalRequests++
        this.stats.minResponseTime = BigIntMath.min(this.stats.minResponseTime ?? took, took)
        this.stats.maxResponseTime = BigIntMath.max(this.stats.maxResponseTime ?? took, took)
        this.stats.avgResponseTime = this.stats.avgResponseTime ? (this.stats.avgResponseTime * BigInt(this.stats.successCount - 1) + took) / BigInt(this.stats.successCount) : took
        this.stats.updatedAt = Date.now()

        this.emit('stats', this.stats)
    }

    public increaseFailedRequest() {
        this.stats.failedCount++
        this.stats.totalRequests++
        this.stats.updatedAt = Date.now()

        this.emit('stats', this.stats)
    }

    public increaseRateLimitReached() {
        this.stats.rateLimitReachedCount++
        this.stats.updatedAt = Date.now()

        this.emit('stats', this.stats)
    }
}
