import type { JsonRpcHttpRequestInfo, JsonRpcHttpResponse } from '../../modules/jsonrpc-server/types'
import type { Endpoint } from '../endpoint'
import type { EndpointManager } from '../manager'
import type { EndpointStats } from '../monitor'
import { message } from '@kdt310722/logger'
import { isAbortError } from '@kdt310722/utils/error'
import { BigIntMath } from '@kdt310722/utils/number'
import { AggregateJsonRpcError } from '../../errors/aggregate-json-rpc-error'
import { JsonRpcError, JsonRpcErrorCode } from '../../errors/json-rpc-error'
import { EndpointDispatchStrategy } from './strategy'

export interface PriorityStats {
    minAvgResponseTime?: bigint
    maxAvgResponseTime?: bigint
    minLatestSlot?: number
    maxLatestSlot?: number
}

export class PriorityBasedFailoverStrategy extends EndpointDispatchStrategy {
    protected readonly stats: PriorityStats = {}
    protected readonly priorities: Record<string, number> = {}

    protected sortedEndpoints: Endpoint[] = []

    public override initialize(manager: EndpointManager) {
        super.initialize(manager)

        for (const endpoint of (this.sortedEndpoints = manager.getEndpoints())) {
            this.registerEventListeners(endpoint)
        }
    }

    public async dispatch(request: JsonRpcHttpRequestInfo): Promise<JsonRpcHttpResponse> {
        const errors: JsonRpcError[] = []

        for (const endpoint of this.sortedEndpoints) {
            if (!endpoint.isAvailable()) {
                continue
            }

            try {
                return await endpoint.send(request.message, { signal: request.signal }).then((r) => this.manager.toJsonRpcHttpResponse(r, endpoint))
            } catch (error) {
                if (isAbortError(error)) {
                    continue
                }

                errors.push(error instanceof JsonRpcError ? error : new JsonRpcError(JsonRpcErrorCode.INTERNAL_ERROR, 'Internal server error', { cause: error }))
            }
        }

        if (errors.length === 0) {
            throw new JsonRpcError(JsonRpcErrorCode.INTERNAL_ERROR, 'No available endpoints')
        }

        throw new AggregateJsonRpcError(errors, JsonRpcErrorCode.INTERNAL_ERROR, 'All endpoints failed')
    }

    protected registerEventListeners(endpoint: Endpoint) {
        endpoint.monitor.on('stats', (stats) => this.handleStats(endpoint, stats))
        endpoint.on('slot', (slot) => this.handleSlot(endpoint, slot))
    }

    protected updateSortedEndpoints() {
        this.sortedEndpoints = this.manager.getEndpoints().sort((a, b) => this.priorities[b.name] - this.priorities[a.name])
        this.logger.debug(message(() => `Sorted endpoints: ${this.sortedEndpoints.map((i) => i.name).join(', ')}`))
    }

    protected updatePriority(endpoint: Endpoint) {
        const oldPriority = this.priorities[endpoint.name]
        const newPriority = this.calculatePriority(this.stats, endpoint.monitor.stats, endpoint.latestSlot)

        if (oldPriority !== newPriority) {
            this.priorities[endpoint.name] = newPriority
            this.updateSortedEndpoints()
        }
    }

    protected handleStats(endpoint: Endpoint, stats: EndpointStats) {
        this.stats.minAvgResponseTime = BigIntMath.min(this.stats.minAvgResponseTime ?? stats.avgResponseTime, stats.avgResponseTime)
        this.stats.maxAvgResponseTime = BigIntMath.max(this.stats.maxAvgResponseTime ?? stats.avgResponseTime, stats.avgResponseTime)
        this.updatePriority(endpoint)
    }

    protected handleSlot(endpoint: Endpoint, slot?: number) {
        this.stats.minLatestSlot = slot ? Math.min(this.stats.minLatestSlot ?? slot, slot) : undefined
        this.stats.maxLatestSlot = slot ? Math.max(this.stats.maxLatestSlot ?? slot, slot) : undefined
        this.updatePriority(endpoint)
    }

    protected calculatePriority(stats: PriorityStats, endpointStats: EndpointStats, latestSlot?: number) {
        const normalizedAvgResponseTime = stats.minAvgResponseTime && stats.maxAvgResponseTime ? (stats.maxAvgResponseTime - endpointStats.avgResponseTime) / (stats.maxAvgResponseTime - stats.minAvgResponseTime + 1n) : 0n
        const normalizedLatestSlot = latestSlot && stats.maxLatestSlot && stats.minLatestSlot && stats.maxLatestSlot !== stats.minLatestSlot ? (latestSlot - stats.minLatestSlot) / (stats.maxLatestSlot - stats.minLatestSlot + 1) : 0

        return (Number(normalizedAvgResponseTime) * 0.7) + (normalizedLatestSlot * 0.3)
    }
}
