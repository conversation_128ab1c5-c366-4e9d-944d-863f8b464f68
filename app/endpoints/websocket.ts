import type { EndpointWebsocketConfig } from '../config/endpoints'
import { highlight, type Logger } from '@kdt310722/logger'
import { createRequestMessage, isJsonRpcErrorResponseMessage, isJsonRpcMessage, isJsonRpcNotifyMessage, isJsonRpcResponseHasNonNullableId, isJsonRpcResponseMessage, type JsonRpcResponseMessage } from '@kdt310722/rpc'
import { Emitter } from '@kdt310722/utils/event'
import { tap, transform, tryCatch } from '@kdt310722/utils/function'
import { formatNanoseconds } from '@kdt310722/utils/number'
import { abortable, createDeferredWithTimeout, type DeferredPromise, pTap, sleep } from '@kdt310722/utils/promise'
import { WebSocket } from 'ws'
import { JsonRpcError } from '../errors/json-rpc-error'
import { TimeoutError } from '../errors/timeout-error'
import { getRetryDelay } from '../utils/sender/retry'

export type EndpointWebSocketClientEvents = {
    reconnectAborted: () => void
}

export class EndpointWebSocketClient extends Emitter<EndpointWebSocketClientEvents, true> {
    protected requests: Record<string, DeferredPromise<JsonRpcResponseMessage>> = {}
    protected incrementRequestId = 0n

    protected ws?: WebSocket
    protected connectPromise?: Promise<void>
    protected disconnectPromise?: DeferredPromise<void>
    protected connectAbortController = new AbortController()
    protected isExplicitlyClosed = false

    protected isAlive = false
    protected pingInterval?: NodeJS.Timeout
    protected pongTimeout?: NodeJS.Timeout

    protected isReconnecting = false
    protected reconnectAttempts = 0
    protected lastReconnectSuccessTime?: number

    protected slotSubscriptionId?: number
    protected slotSubscribePromise?: Promise<void>
    protected slotUnsubscribePromise?: Promise<void>
    protected slotHandlers = new Set<(slot: number) => void>()

    public constructor(protected readonly name: string, protected readonly config: EndpointWebsocketConfig, protected readonly logger: Logger) {
        super()
    }

    public async subscribeSlot(onSlot: (slot: number) => void) {
        const unsubscribe = async () => {
            this.slotHandlers.delete(onSlot)

            if (this.slotHandlers.size === 0 && this.slotSubscriptionId) {
                await (this.slotUnsubscribePromise ??= this.send('slotUnsubscribe', [this.slotSubscriptionId])).then(() => this.slotUnsubscribePromise = undefined).then(() => this.slotSubscriptionId = undefined)
            }
        }

        if (this.slotSubscribePromise) {
            return this.slotSubscribePromise.then(() => this.slotHandlers.add(onSlot)).then(() => unsubscribe)
        }

        if (this.slotSubscriptionId) {
            return Promise.resolve(this.slotHandlers.add(onSlot)).then(() => unsubscribe)
        }

        const subscribe = async () => {
            this.slotSubscriptionId = await this.send<number>('slotSubscribe')
        }

        return (this.slotSubscribePromise = subscribe()).then(() => this.slotHandlers.add(onSlot)).then(() => unsubscribe).finally(() => this.slotSubscribePromise = undefined)
    }

    public async send<TResult = any, TParams = any>(method: string, params?: TParams): Promise<TResult> {
        if (!this.ws) {
            throw new Error('WebSocket is not connected')
        }

        this.connectAbortController.signal.throwIfAborted()

        const id = String(++this.incrementRequestId)
        const responsePromise = createDeferredWithTimeout<JsonRpcResponseMessage>(this.config.timeout.request, () => new TimeoutError('WebSocket request timed out'))

        let abortHandler: () => void

        this.requests[id] = responsePromise
        this.connectAbortController.signal.addEventListener('abort', abortHandler = () => responsePromise.reject(this.connectAbortController.signal.reason), { once: true })

        try {
            const response = await Promise.resolve(this.ws.send(JSON.stringify(createRequestMessage(id, method, params)), (error) => error && responsePromise.reject(error))).then(() => responsePromise)

            if (isJsonRpcErrorResponseMessage(response)) {
                throw JsonRpcError.fromErrorObject(response.error)
            }

            return response.result as TResult
        } finally {
            delete this.requests[id]
            this.connectAbortController.signal.removeEventListener('abort', abortHandler)
        }
    }

    public async connect() {
        if (this.disconnectPromise) {
            await this.disconnectPromise
        }

        return this.connectPromise ??= this.createConnection(this.connectAbortController.signal).finally(() => this.connectPromise = undefined)
    }

    public async disconnect(isExplicitly = true, error?: unknown) {
        if (error) {
            this.logError(error)
        }

        if (!this.ws) {
            return
        }

        this.isExplicitlyClosed = isExplicitly

        if (this.disconnectPromise) {
            if (isExplicitly) {
                this.connectAbortController.abort()
            }

            return this.disconnectPromise
        }

        this.disconnectPromise = createDeferredWithTimeout<void>(this.config.timeout.disconnect, () => new TimeoutError('WebSocket disconnection timed out'))

        try {
            this.closeOrTerminate()
        } catch (error) {
            this.disconnectPromise.reject(error)
        }

        try {
            await this.disconnectPromise
        } catch (error) {
            this.logError(error)
            this.ws.terminate()
        } finally {
            this.disconnectPromise = undefined
        }
    }

    protected closeOrTerminate() {
        try {
            this.ws?.close()
        } catch (error) {
            this.logger.error(`Failed to close WebSocket connection for endpoint ${highlight(this.name)}`, error)

            try {
                this.ws?.terminate()
            } catch (error_) {
                throw new AggregateError([error, error_], 'Failed to disconnect WebSocket')
            }
        }
    }

    protected reset() {
        this.ws?.removeAllListeners()
        this.ws = undefined
        this.resetHeartbeat()
        this.disconnectPromise?.resolve()
        this.connectAbortController = new AbortController()
        this.isExplicitlyClosed = false
        this.requests = {}
        this.incrementRequestId = 0n
        this.slotSubscriptionId = undefined
    }

    protected async createConnection(signal?: AbortSignal) {
        signal?.throwIfAborted()

        if (this.ws) {
            throw new Error('Already has a WebSocket instance')
        }

        this.logger.info(`Connecting to WebSocket for endpoint ${highlight(this.name)}...`)

        const promise = createDeferredWithTimeout<void>(this.config.timeout.connect, () => new TimeoutError('WebSocket connection timed out'))
        const ws = this.ws = new WebSocket(this.config.url)

        const handleAbort = () => {
            if (!promise.isSettled) {
                if (ws.readyState !== WebSocket.CLOSED) {
                    ws.terminate()
                }

                this.reset()
                promise.reject(signal?.reason)
            }
        }

        signal?.addEventListener('abort', handleAbort, { once: true })

        ws.on('close', (code, reason) => this.handleClose(promise, code, reason))
        ws.on('error', (error) => this.handleError(promise, error))
        ws.on('open', () => this.handleOpen(promise))
        ws.on('pong', () => this.handlePong())
        ws.on('message', (message) => this.handleMessage(message))

        return promise.catch(pTap.catch(() => this.reset())).finally(() => {
            signal?.removeEventListener('abort', handleAbort)
        })
    }

    protected handlePong() {
        this.resolveHeartbeat()
    }

    protected handleMessage(message: WebSocket.Data) {
        this.resolveHeartbeat()

        const stringified = tryCatch(() => message.toString('utf8'), message)
        const parsed = tryCatch(() => JSON.parse(stringified.toString('utf8')), stringified)

        if (isJsonRpcMessage(parsed)) {
            if (isJsonRpcNotifyMessage(parsed)) {
                if (this.slotSubscriptionId && parsed.method === 'slotNotification' && parsed.params?.subscription === this.slotSubscriptionId) {
                    return this.slotHandlers.forEach((handler) => handler(parsed.params.result.slot))
                }

                return this.logger.warn(`Unhandled notification received from WebSocket for endpoint ${highlight(this.name)}`, parsed)
            }

            if (isJsonRpcResponseMessage(parsed) && isJsonRpcResponseHasNonNullableId(parsed)) {
                const request = this.requests[parsed.id]

                if (request) {
                    return request.resolve(parsed)
                }
            }
        }

        this.logger.warn(`Unhandled message received from WebSocket for endpoint ${highlight(this.name)}`, parsed)
    }

    protected handleClose(connectPromise: DeferredPromise<void>, code: number, reason: Buffer) {
        if (this.disconnectPromise) {
            this.disconnectPromise.resolve()
        }

        for (const request of Object.values(this.requests)) {
            request.reject(new Error(`WebSocket connection for endpoint ${this.name} closed`))
        }

        const reason_ = `${code} - ${transform(reason.toString(), (reason) => (reason.length > 0 ? reason : 'Unknown reason'))}`

        if (!connectPromise.isSettled) {
            return connectPromise.reject(new Error(`WebSocket connection for endpoint ${highlight(this.name)} closed before it was established: ${reason_}`))
        }

        if (connectPromise.isRejected) {
            return
        }

        const isExplicitlyClosed = this.isExplicitlyClosed
        const isAborted = this.connectAbortController.signal.aborted
        const resubscribeSlot = !!this.slotSubscriptionId

        this.reset()
        this.logger.log(isExplicitlyClosed ? 'info' : 'warn', `WebSocket connection for endpoint ${highlight(this.name)} closed: ${highlight(reason_)}`)

        if (isExplicitlyClosed) {
            this.slotHandlers.clear()
        } else if (!isAborted && this.config.reconnect.enabled) {
            this.reconnect(resubscribeSlot)
        }
    }

    protected handleError(connectPromise: DeferredPromise<void>, error: Error) {
        if (!connectPromise.isSettled) {
            return connectPromise.reject(error)
        }

        this.logError(error)
        this.disconnect(false)
    }

    protected handleOpen(connectPromise: DeferredPromise<void>) {
        this.logger.info(`WebSocket connection for endpoint ${highlight(this.name)} established!`)

        if (!connectPromise.isSettled) {
            return connectPromise.resolve()
        }

        if (this.config.heartbeat.enabled) {
            this.startHeartbeat()
        }
    }

    protected logError(error: unknown) {
        this.logger.error(`Error occurred in WebSocket connection for endpoint ${highlight(this.name)}`, error)
    }

    protected startHeartbeat() {
        this.isAlive = true
        this.pingInterval = setInterval(() => this.runHeartbeat(), this.config.heartbeat.interval)
    }

    protected runHeartbeat() {
        if (!this.ws) {
            return
        }

        clearTimeout(this.pongTimeout)

        if (!this.isAlive) {
            return this.handleHeartbeatTimeout()
        }

        this.isAlive = false
        this.ws.ping()
        this.pongTimeout = setTimeout(() => this.isAlive || this.handleHeartbeatTimeout(), this.config.heartbeat.timeout)
    }

    protected handleHeartbeatTimeout() {
        this.logger.error(`Heartbeat timeout for WebSocket connection for endpoint ${highlight(this.name)}`)
        this.disconnect(false)
    }

    protected resolveHeartbeat() {
        this.isAlive = true
        clearTimeout(this.pongTimeout)
    }

    protected resetHeartbeat() {
        this.isAlive = false
        clearInterval(this.pingInterval)
        clearTimeout(this.pongTimeout)
    }

    protected async reconnect(resubscribeSlot: boolean) {
        if (this.connectAbortController.signal.aborted) {
            return
        }

        const abandon = (reason: string, error?: unknown, emit = true) => {
            if (error) {
                this.logError(error)
            }

            this.isReconnecting = false
            this.logger.error(`Reconnection to WebSocket for endpoint ${highlight(this.name)} abandoned: ${highlight(reason)}`)

            if (emit) {
                this.emit('reconnectAborted')
            }
        }

        if (this.reconnectAttempts >= this.config.reconnect.retries) {
            return abandon('Max attempts reached')
        }

        if (this.lastReconnectSuccessTime && Date.now() - this.lastReconnectSuccessTime < this.config.reconnect.circuitBreakerTimeout) {
            return abandon('Circuit breaker tripped')
        }

        if (this.isReconnecting) {
            return
        }

        this.isReconnecting = true
        this.reconnectAttempts++

        const delay = tap(getRetryDelay(this.reconnectAttempts, this.config.reconnect), (delay) => this.logger.info(`Waiting ${highlight(formatNanoseconds(BigInt(delay * 1e6)))} before reconnecting to WebSocket for endpoint ${highlight(this.name)}...`))
        const isWaited = await abortable(sleep(delay), this.connectAbortController.signal).then(() => true).catch(() => false)

        if (!isWaited) {
            return abandon('Aborted', undefined, false)
        }

        try {
            await this.connect().then(() => this.lastReconnectSuccessTime = Date.now())

            if (resubscribeSlot) {
                try {
                    await this.subscribeSlot(() => void 0)
                } catch (error) {
                    await this.disconnect(false)
                    throw error
                }
            }

            this.reconnectAttempts = 0
        } catch (error) {
            this.logger.error(`Failed to reconnect to WebSocket for endpoint ${highlight(this.name)}`, error)
            this.isReconnecting = false
            this.reconnect(resubscribeSlot)
        } finally {
            this.isReconnecting = false
        }
    }
}
