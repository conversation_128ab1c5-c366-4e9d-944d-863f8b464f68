import type { EndpointConfig } from '../config/endpoints'
import { highlight } from '@kdt310722/logger'
import { tap } from '@kdt310722/utils/function'
import { EndpointWithWarmup } from './endpoint-with-warmup'
import { EndpointWebSocketClient } from './websocket'

export class Endpoint extends EndpointWithWarmup {
    public websocket?: EndpointWebSocketClient
    public latestSlot?: number

    public constructor(config: EndpointConfig) {
        super(config)

        if (config.websocket) {
            this.websocket = this.createWebSocketClient(config.websocket)
        }
    }

    public isAvailable() {
        return this.isEnabled && (!this.config.http.maxConcurrentRequests || this.sender.activeRequests < this.config.http.maxConcurrentRequests - 1)
    }

    public async initialize() {
        const timer = tap(this.logger.createTimer(), () => this.logger.info(`Initializing endpoint ${highlight(this.name)}...`))

        await this.enableWarmupIfSupported()
        await this.subscribeSlotUpdate()

        this.logger.stopTimer(timer, 'info', `Endpoint ${highlight(this.name)} initialized!`)
    }

    protected async subscribeSlotUpdate() {
        if (!this.websocket) {
            return
        }

        const timer = tap(this.logger.createTimer(), () => this.logger.info(`Subscribing to slot updates for endpoint ${highlight(this.name)}...`))

        await this.websocket.connect()
        await this.websocket.subscribeSlot((slot) => this.emit('slot', this.latestSlot = slot))

        this.logger.stopTimer(timer, 'info', `Subscribed to slot updates for endpoint ${highlight(this.name)}!`)
    }

    protected createWebSocketClient(config: NonNullable<EndpointConfig['websocket']>) {
        const client = new EndpointWebSocketClient(this.name, config, this.logger)

        client.on('reconnectAborted', () => {
            this.emit('slot', this.latestSlot = undefined)
        })

        return client
    }
}
