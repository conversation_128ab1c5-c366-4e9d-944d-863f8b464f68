import { isBoolean } from '@kdt310722/utils/common'
import z from 'zod'

const schema = z.object({
    enabled: z.boolean().default(true),
    retries: z.number().int().nonnegative().default(3),
    delay: z.number().nonnegative().default(1000),
    backoff: z.number().nonnegative().default(2),
    jitter: z.number().nonnegative().max(1).default(0.1),
    maxDelay: z.number().nonnegative().default(10_000),
    circuitBreakerTimeout: z.number().positive().default(5000),
})

export const retry = z.union([schema, z.boolean()]).transform((val) => (isBoolean(val) ? schema.parse({ enabled: val }) : val))
