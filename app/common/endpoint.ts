import { config } from '../config'
import { setJsonRpcRequestHandler } from '../core/server'
import { EndpointManager } from '../endpoints/manager'
import { ParallelStrategy } from '../endpoints/strategies/parallel'
import { PriorityBasedFailoverStrategy } from '../endpoints/strategies/priority‑based-failover'

const manager = new EndpointManager(config.endpoints, [
    new ParallelStrategy(),
    new PriorityBasedFailoverStrategy(),
])

setJsonRpcRequestHandler(async (request) => {
    return manager.dispatch(request)
})

export async function initializeEndpointManager() {
    await manager.initialize()
}
